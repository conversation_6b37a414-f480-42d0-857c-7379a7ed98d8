{"_from": "rechoir@^0.7.0", "_id": "rechoir@0.7.1", "_inBundle": false, "_integrity": "sha512-/njmZ8s1wVeR6pjTZ+0nCnv8SpZNRMT2D1RLOJQESlYFDBvwpTA4KWJpZ+sBJ4+vhjILRcK7JIFdGCdxEAAitg==", "_location": "/webpack-cli/rechoir", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "rechoir@^0.7.0", "name": "rechoir", "escapedName": "rechoir", "rawSpec": "^0.7.0", "saveSpec": null, "fetchSpec": "^0.7.0"}, "_requiredBy": ["/webpack-cli"], "_resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.7.1.tgz", "_shasum": "9478a96a1ca135b5e88fc027f03ee92d6c645686", "_spec": "rechoir@^0.7.0", "_where": "E:\\xamp81\\htdocs\\amdrupal\\pantheon\\electiond10\\web\\modules\\custom\\elections\\node_modules\\webpack-cli", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/rechoir/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "dependencies": {"resolve": "^1.9.0"}, "deprecated": false, "description": "Prepare a node environment to require files with different extensions.", "devDependencies": {"eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "istanbul": "^0.4.3", "istanbul-coveralls": "^1.0.3", "mocha": "^3.5.3"}, "engines": {"node": ">= 0.10"}, "files": ["LICENSE", "index.js", "lib/"], "homepage": "https://github.com/gulpjs/rechoir#readme", "keywords": ["require", "loader", "extension", "extensions", "prepare"], "license": "MIT", "main": "index.js", "name": "rechoir", "repository": {"type": "git", "url": "git+https://github.com/gulpjs/rechoir.git"}, "scripts": {"cover": "istanbul cover _mocha --report lcovonly test test/lib", "coveralls": "npm run cover && istanbul-coveralls", "lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "mocha --async-only test test/lib"}, "version": "0.7.1"}