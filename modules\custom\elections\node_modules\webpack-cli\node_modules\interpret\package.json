{"_from": "interpret@^2.2.0", "_id": "interpret@2.2.0", "_inBundle": false, "_integrity": "sha512-Ju0Bz/cEia55xDwUWEa8+olFpCiQoypjnQySseKtmjNrnps3P+xfpUmGr90T7yjlVJmOtybRvPXhKMbHr+fWnw==", "_location": "/webpack-cli/interpret", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "interpret@^2.2.0", "name": "interpret", "escapedName": "interpret", "rawSpec": "^2.2.0", "saveSpec": null, "fetchSpec": "^2.2.0"}, "_requiredBy": ["/webpack-cli"], "_resolved": "https://registry.npmjs.org/interpret/-/interpret-2.2.0.tgz", "_shasum": "1a78a0b5965c40a5416d007ad6f50ad27c417df9", "_spec": "interpret@^2.2.0", "_where": "E:\\xamp81\\htdocs\\amdrupal\\pantheon\\electiond10\\web\\modules\\custom\\elections\\node_modules\\webpack-cli", "author": {"name": "Gulp Team", "email": "<EMAIL>", "url": "http://gulpjs.com/"}, "bugs": {"url": "https://github.com/gulpjs/interpret/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://goingslowly.com/"}], "dependencies": {}, "deprecated": false, "description": "A dictionary of file extensions and associated module loaders.", "devDependencies": {"coveralls": "github:phated/node-coveralls#2.x", "eslint": "^2.13.0", "eslint-config-gulp": "^3.0.1", "expect": "^1.20.2", "mocha": "^3.5.3", "nyc": "^10.3.2", "parse-node-version": "^1.0.0", "rechoir": "^0.7.0", "shelljs": "0.7.5", "trash-cli": "^3.0.0"}, "engines": {"node": ">= 0.10"}, "files": ["LICENSE", "index.js", "mjs-stub.js"], "homepage": "https://github.com/gulpjs/interpret#readme", "keywords": ["cirru-script", "cjsx", "co", "coco", "coffee", "coffee-script", "coffee.md", "coffeescript", "csv", "<PERSON><PERSON><PERSON>", "es", "es6", "iced", "iced.md", "iced-coffee-script", "ini", "js", "json", "json5", "jsx", "react", "litcoffee", "liticed", "ls", "livescript", "toml", "ts", "typescript", "wisp", "xml", "yaml", "yml"], "license": "MIT", "main": "index.js", "name": "interpret", "repository": {"type": "git", "url": "git+https://github.com/gulpjs/interpret.git"}, "scripts": {"cover": "nyc --reporter=lcov --reporter=text-summary npm test", "coveralls": "nyc --reporter=text-lcov npm test | coveralls", "lint": "eslint .", "pretest": "rm -rf tmp/ && npm run lint", "test": "mocha --async-only"}, "version": "2.2.0"}